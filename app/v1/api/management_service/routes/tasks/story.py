from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Optional, List, Dict, Any
from bson import ObjectId
from pydantic import BaseModel, Field
from datetime import datetime
from app.shared.security import get_tenant_info
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from app.v1.schema.pagination import PaginationResponse, PaginationMeta

loggers = setup_new_logging(__name__)

router = APIRouter()


class StoryStep(BaseModel):
    """Model for a single story step."""
    stage: int = Field(description="Stage number")
    script: str = Field(description="Nepali script for this stage")
    image: str = Field(description="Image prompt for this stage")
    media: Optional[dict] = Field({}, description="Media object for the image")


class StoryStepResponse(BaseModel):
    """Model for story step response with metadata."""
    step: StoryStep = Field(description="Story step data")
    story_id: str = Field(description="Story ID")
    total_steps: int = Field(description="Total number of steps in the story")
    completed_steps: int = Field(description="Number of completed steps")
    status: str = Field(description="Story status")
    created_at: datetime = Field(description="Story creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Story update timestamp")


class StoryListItem(BaseModel):
    """Model for story list item with first step only."""
    story_id: str = Field(description="Story ID")
    first_step_script: str = Field(description="Script from the first step")
    first_step_image: str = Field(description="Image prompt from the first step")
    media_url: str = Field(None, description="Media URL for the first step")
    total_steps: int = Field(description="Total number of steps in the story")
    completed_steps: int = Field(description="Number of completed steps")
    status: str = Field(description="Story status")
    created_at: datetime = Field(description="Story creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Story update timestamp")


@router.get("/{story_id}")
async def get_story_step(
    story_id: str,
    stage: int = Query(1, ge=1, description="Stage number to retrieve"),
    user_tenant: UserTenantDB = Depends(get_tenant_info)
) -> PaginationResponse[StoryStepResponse]:
    """
    Get a specific story step by ID with pagination-like response.

    Args:
        story_id: The story ID
        stage: Stage number to retrieve (default: 1)
        user_tenant: User tenant information

    Returns:
        Paginated response containing the requested story step
    """
    try:
        loggers.info(f"Getting story {story_id} stage {stage} for user {user_tenant.user.username}")

        # Find the story document with the specific stage using MongoDB aggregation
        pipeline = [
            {"$match": {"_id": ObjectId(story_id)}},
            {
                "$project": {
                    "total_steps": 1,
                    "completed_steps": 1,
                    "status": 1,
                    "created_at": 1,
                    "updated_at": 1,
                    "requested_step": {
                        "$filter": {
                            "input": "$steps",
                            "cond": {"$eq": ["$$this.stage", stage]}
                        }
                    }
                }
            }
        ]

        cursor = user_tenant.async_db.story_steps.aggregate(pipeline)
        result = await (await cursor).to_list(length=1)

        if not result:
            raise HTTPException(status_code=404, detail=f"Story {story_id} not found")

        story = result[0]
        requested_steps = story.get("requested_step", [])

        if not requested_steps:
            total_steps = story.get("total_steps", 0)
            raise HTTPException(status_code=404, detail=f"Stage {stage} not found. Story has {total_steps} total steps")

        requested_step = requested_steps[0]  # Should only be one matching step
        total_steps = story.get("total_steps", 0)

        # Create response data
        step_response = StoryStepResponse(
                step=StoryStep(**requested_step,media={
                "object_name": "story_image.jpg",
                "folder": "story_images",
                "media_type": "image",
                "bucket_name": "nepali-app-media",
                "content_type": "image/jpeg",
                "size_bytes": 245760,
                "url": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwcHBwcHBwcHBwgHBw0HDQ0HBw8ICQ0NFREWFiARExMYKCggGBolGxMfITEhMSkrLi4uFx8zODMsNygtLjcBCgoKDQ0NDg0NDisZFRkrLSsrKzcrKystLSsrKysrKysrKystNystNysrKysrKy0rKysrKysrKysrKysrKysrK//AABEIASsAqAMBIgACEQEDEQH/xAAaAAEBAQEAAwAAAAAAAAAAAAAAAQIFAwQG/8QAFhABAQEAAAAAAAAAAAAAAAAAAAER/8QAGAEBAQEBAQAAAAAAAAAAAAAAAAECAwT/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwD4gB7nhAAEAABAQEAAUAQAEABFAAAEGgHZkQAAEBAQABQBAAQAEUAAAQAEGkB3ZAEBAQABQBAAQAEUAAAQAEAEBoB2ZEBAAFAEABAARQAABAAQAQABFaQHZgAFAEABAARQAABAAQAQAERQKgNgOzIAgAIACKAAAIACACAAiKAgFGVB5AHVkAQAEUAAAQAEAEABEUBKBayJQLRAV5wHRgARQAABAAQAQAERQEtAZEoFSlQUGbQHtANsgAACAAgAgAIigJaBUGaBUogozS1LQLRm1Qe4A2yAIACACAAiKAloFrKs0CoVBRm0qWgVm0tSgUZBXQAaYAEAEABEUBAKgzQKlKgozSpQLWbS1KBWRKKCUB0gFYAQAERQEoFZEoFQqCjNpUtArNpalAtZpUoolKzaBarNoK6oIrmAiKAgJUEoFSlQUZtLUtArNpaloFZogolLUtBLUKlFKIgOuCDACWgMqzQKggozaWpaBWbS1KBWRBRKVm0C1KVKKVlWQKJaA7AJRgqIlAqFQUZtLUtAtZtLUoFZogolKloFrNpUoogzQEtLUFKM0B2tZEo5lSiCjNKloFrNpalArJUFEpUoFZpalopagzQKlpalFKzaWoAIA7NQQYGbSpaBWbS1LQLWaJRSpaVKBazaVLRSoM0CpaWpRRmlqAIIAICuyzVtZtHMrNpalAtZEopUpUoFZpUopUGQEtLUopWaVAEEAQQUQAdi1m1azRzKyrNFEtKlAtZpUopaglBEtKlFKzaVAEEAQSiiAAAD/9k="
            }),            story_id=story_id,
            total_steps=total_steps,
            completed_steps=story.get("completed_steps", 0),
            status=story.get("status", "unknown"),
            created_at=story.get("created_at"),
            updated_at=story.get("updated_at")
        )

        # Return paginated response format
        return PaginationResponse(
            data=[step_response],
            meta=PaginationMeta(
                page=stage,
                limit=1,
                total=total_steps,
                total_pages=total_steps
            )
        )

    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error getting story step: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting story step: {str(e)}")


@router.get("/all/filtered")
async def get_filtered_stories(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Items per page"),
    user_tenant: UserTenantDB = Depends(get_tenant_info)
) -> PaginationResponse[StoryListItem]:
    """
    Get all stories with pagination, showing only the first step's script and image for each story.

    Args:
        page: Page number (default: 1)
        limit: Number of items per page (default: 10, max: 100)
        user_tenant: User tenant information

    Returns:
        Paginated response containing story list items with first step data
    """
    try:
        loggers.info(f"Getting filtered stories for user {user_tenant.user.username}, page={page}, limit={limit}")

        # Calculate skip value for pagination
        skip = (page - 1) * limit

        # Build aggregation pipeline to get stories with first step only
        pipeline = [
            # Match stories for the current user (optional - remove if you want all stories)
            {"$match": {"user_id": str(user_tenant.user.id)}},

            # Add first step data
            {
                "$addFields": {
                    "first_step": {"$arrayElemAt": ["$steps", 0]}
                }
            },

            # Project only needed fields
            {
                "$project": {
                    "_id": 1,
                    "total_steps": 1,
                    "completed_steps": 1,
                    "status": 1,
                    "created_at": 1,
                    "updated_at": 1,
                    "first_step_script": "$first_step.script",
                    "first_step_image": "$first_step.image"
                }
            },

            # Sort by creation date (newest first)
            {"$sort": {"created_at": -1}},

            # Facet for pagination
            {
                "$facet": {
                    "metadata": [{"$count": "total"}],
                    "data": [
                        {"$skip": skip},
                        {"$limit": limit}
                    ]
                }
            }
        ]

        cursor = user_tenant.async_db.story_steps.aggregate(pipeline)
        result = await (await cursor).to_list(length=1)

        if not result:
            return PaginationResponse(
                data=[],
                meta=PaginationMeta(page=page, limit=limit, total=0, total_pages=0)
            )

        pipeline_result = result[0]
        stories_data = pipeline_result.get("data", [])
        total = pipeline_result.get("metadata", [{}])[0].get("total", 0)

        # Convert to StoryListItem objects
        story_items = []
        for story in stories_data:
            # Skip stories without first step data
            if not story.get("first_step_script") or not story.get("first_step_image"):
                continue

            story_item = StoryListItem(
                story_id=str(story["_id"]),
                first_step_script=story["first_step_script"],
                first_step_image=story["first_step_image"],
                media_url="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwcHBwcHBwcHBwgHBw0HDQ0HBw8ICQ0NFREWFiARExMYKCggGBolGxMfITEhMSkrLi4uFx8zODMsNygtLjcBCgoKDQ0NDg0NDisZFRkrLSsrKzcrKystLSsrKysrKysrKystNystNysrKysrKy0rKysrKysrKysrKysrKysrK//AABEIASsAqAMBIgACEQEDEQH/xAAaAAEBAQEAAwAAAAAAAAAAAAAAAQIFAwQG/8QAFhABAQEAAAAAAAAAAAAAAAAAAAER/8QAGAEBAQEBAQAAAAAAAAAAAAAAAAECAwT/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwD4gB7nhAAEAABAQEAAUAQAEABFAAAEGgHZkQAAEBAQABQBAAQAEUAAAQAEGkB3ZAEBAQABQBAAQAEUAAAQAEAEBoB2ZEBAAFAEABAARQAABAAQAQABFaQHZgAFAEABAARQAABAAQAQAERQKgNgOzIAgAIACKAAAIACACAAiKAgFGVB5AHVkAQAEUAAAQAEAEABEUBKBayJQLRAV5wHRgARQAABAAQAQAERQEtAZEoFSlQUGbQHtANsgAACAAgAgAIigJaBUGaBUogozS1LQLRm1Qe4A2yAIACACAAiKAloFrKs0CoVBRm0qWgVm0tSgUZBXQAaYAEAEABEUBAKgzQKlKgozSpQLWbS1KBWRKKCUB0gFYAQAERQEoFZEoFQqCjNpUtArNpalAtZpUoolKzaBarNoK6oIrmAiKAgJUEoFSlQUZtLUtArNpaloFZogolLUtBLUKlFKIgOuCDACWgMqzQKggozaWpaBWbS1KBWRBRKVm0C1KVKKVlWQKJaA7AJRgqIlAqFQUZtLUtAtZtLUoFZogolKloFrNpUoogzQEtLUFKM0B2tZEo5lSiCjNKloFrNpalArJUFEpUoFZpalopagzQKlpalFKzaWoAIA7NQQYGbSpaBWbS1LQLWaJRSpaVKBazaVLRSoM0CpaWpRRmlqAIIAICuyzVtZtHMrNpalAtZEopUpUoFZpUopUGQEtLUopWaVAEEAQQUQAdi1m1azRzKyrNFEtKlAtZpUopaglBEtKlFKzaVAEEAQSiiAAAD/9k=",
                total_steps=story.get("total_steps", 0),
                completed_steps=story.get("completed_steps", 0),
                status=story.get("status", "unknown"),
                created_at=story.get("created_at"),
                updated_at=story.get("updated_at")
            )
            story_items.append(story_item)

        # Calculate total pages
        total_pages = (total + limit - 1) // limit if total > 0 else 0

        return PaginationResponse(
            data=story_items,
            meta=PaginationMeta(
                page=page,
                limit=limit,
                total=total,
                total_pages=total_pages
            )
        )

    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error getting filtered stories: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting filtered stories: {str(e)}")


@router.get("/set/{task_set_id}/stories")
async def get_stories_for_task_set(
    task_set_id: str,
    fields: Optional[List[str]] = Query(
        default=["steps", "total_steps", "completed_steps", "status", "created_at", "updated_at"],
        description="Fields to retrieve from stories"
    ),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get all stories for a specific task set ID.

    Args:
        task_set_id: The task set ID
        fields: Fields to retrieve from stories
        current_user: Current user information

    Returns:
        List of stories for the specified task set
    """
    try:
        loggers.info(f"Getting stories for task set {task_set_id} for user {current_user.user.username}")

        # Validate task_set_id
        if not task_set_id or not ObjectId.is_valid(task_set_id):
            raise HTTPException(status_code=400, detail="Invalid task set ID")

        # Create projection for MongoDB query
        projection = {field: 1 for field in fields}
        projection["_id"] = 1  # Always include _id

        # Get all stories for this task set
        stories = []

        # Query stories by task_set_id, collection_id, or v2_collection_id
        query = {"$or": [
            {"task_set_id": task_set_id},
            {"collection_id": task_set_id},
            {"v2_collection_id": task_set_id}
        ]}

        cursor = current_user.async_db.story_steps.find(query, projection)
        stories = await cursor.to_list(length=None)

        # Convert ObjectIds to strings
        for story in stories:
            story["id"] = str(story.pop("_id"))

        # Return the stories
        return {
            "stories": stories,
            "count": len(stories),
            "task_set_id": task_set_id
        }
    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error getting stories for task set: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting stories for task set: {str(e)}")


@router.get("/story/{story_id}")
async def get_story_by_id(
    story_id: str,
    fields: Optional[List[str]] = Query(
        default=["steps", "total_steps", "completed_steps", "status", "created_at", "updated_at"],
        description="Fields to retrieve from story"
    ),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get a single story by ID.

    Args:
        story_id: The story ID
        fields: Fields to retrieve from story
        current_user: Current user information

    Returns:
        The story with selected fields
    """
    try:
        loggers.info(f"Getting story {story_id} for user {current_user.user.username}")

        # Validate story_id
        if not story_id or not ObjectId.is_valid(story_id):
            raise HTTPException(status_code=400, detail="Invalid story ID")

        # Create projection for MongoDB query
        projection = {field: 1 for field in fields}
        projection["_id"] = 1  # Always include _id

        # Get the story
        story = await current_user.async_db.story_steps.find_one(
            {"_id": ObjectId(story_id)}, projection
        )

        if not story:
            raise HTTPException(status_code=404, detail=f"Story {story_id} not found")

        # Convert ObjectId to string
        story["id"] = str(story.pop("_id"))

        return story
    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error getting story: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting story: {str(e)}")
